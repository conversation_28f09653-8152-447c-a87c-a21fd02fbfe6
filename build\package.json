{"name": "myapp", "version": "0.1.0", "private": true, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.13", "@mui/material": "^5.15.13", "@mui/x-charts": "^6.19.5", "@popperjs/core": "^2.11.8", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.8", "bootstrap": "^5.3.3", "cdbreact": "^1.5.18", "chart.js": "^4.4.2", "chartist": "^0.10.1", "chroma-js": "^2.4.2", "js-cookie": "^3.0.5", "material-ui": "^1.0.0-beta.46", "material-ui-icons": "^1.0.0-beta.36", "mdb-react-ui-kit": "^7.2.0", "npm-run-all": "^4.1.5", "perfect-scrollbar": "^1.5.5", "prop-types": "^15.8.1", "react": "^18.2.0", "react-bootstrap": "^2.10.1", "react-chartist": "^0.14.4", "react-chartjs-2": "^5.2.0", "react-countup": "^6.5.0", "react-dom": "^18.2.0", "react-google-maps": "^9.4.5", "react-icons": "^5.0.1", "react-popper": "^2.3.0", "react-router-dom": "^6.22.3", "react-script-tag": "^1.1.2", "react-scripts": "5.0.1", "react-swipeable-views": "^0.14.0", "react-table": "^7.8.0", "react-toastify": "^10.0.5", "reactjs-popup": "^2.0.6", "scrollreveal": "^4.0.9", "stylis": "^4.3.1", "stylis-plugin-rtl": "^2.1.1", "tableau-api": "^2.2.3", "tableau-react": "^2.2.0", "web-vitals": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000/"}