# Online Voting System

This repository contains the source code for an Online Voting System developed using the MERN stack (MongoDB, Express.js, React.js, Node.js). The system allows users to participate in various types of online voting, such as elections, polls, surveys, and more.

![home page](https://github.com/gurneeshs/Online-Voting-System/assets/99495469/780a5cea-75a6-4990-bf7c-a2018f509ed9)

![home page about](https://github.com/gurneeshs/Online-Voting-System/assets/99495469/5f6284a8-cd6d-4063-bc09-47f44b34bd09)

![registration](https://github.com/gurneeshs/Online-Voting-System/assets/99495469/5348d29b-d02e-430a-91bb-f384048d916f)

![login](https://github.com/gurneeshs/Online-Voting-System/assets/99495469/5d49a581-d416-4f28-9b0d-9b704a0fa2a7)

![user portal](https://github.com/gurneeshs/Online-Voting-System/assets/99495469/16070394-2e9a-40a9-9dbc-dfb3e98d9140)

![user portal upcoming](https://github.com/gurneeshs/Online-Voting-System/assets/99495469/3976ca1e-9cc5-404c-9d78-ac34abb3dae0)

![Voting Page](https://github.com/gurneeshs/Online-Voting-System/assets/99495469/87e968bd-bc63-4bf6-913a-9a33b93485a8)

![Admin Page](https://github.com/gurneeshs/Online-Voting-System/assets/99495469/bb45ae97-7fe7-4bf4-884c-b98e9ea2f53b)

![Mongo DB Schemas](https://github.com/gurneeshs/Online-Voting-System/assets/99495469/03c8ca6c-1719-4f99-bdfe-129419d60c91)






## Features
- **User Authentication:** Secure user authentication and authorization system.
- **Voting Dashboard:** Interactive dashboard for users to view ongoing and upcoming voting events.
- **Voting Interface:** Intuitive interface for users to cast their votes.
- **Admin Panel:** Admin interface to create, manage, and monitor voting events.
- **Real-time Updates:** Real-time updates using WebSocket for instant notifications on voting results.
- **Data Security:** Implementation of security measures to ensure data privacy and integrity.

## Technologies Used
- **Frontend:** React.js for building the user interface.
- **Backend:** Node.js and Express.js for server-side logic.
- **Database:** MongoDB for storing user data, voting events, and results.
- **Authentication:** JSON Web Tokens (JWT) for user authentication.
- **Real-time Updates:** WebSocket for real-time communication between clients and the server.
- **UI Framework:** Material-UI for designing responsive and modern UI components.

## Getting Started
To run the project locally, follow these steps:

1. Clone this repository to your local machine.
2. Navigate to the project directory.
3. Install dependencies for both the server and client:
  - cd server
  - npm install
  - cd ../client
  - npm install

4. Configure environment variables:
Create a .env file in the server directory.
Add necessary environment variables (e.g., MongoDB connection string, JWT secret).

5. Start the server:
cd server
npm start

6. Start the client:
cd ../client
npm start

## Contributing
Contributions are welcome! Please feel free to submit bug reports, feature requests, or pull requests.

- Fork the repository.
- Create a new branch (git checkout -b feature/fooBar).
- Commit your changes (git commit -am 'Add some fooBar').
- Push to the branch (git push origin feature/fooBar).
- Create a new Pull Request.
