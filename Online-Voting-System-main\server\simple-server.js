const express = require('express');
const cors = require('cors');
const path = require('path');
const multer = require('multer');
const fs = require('fs');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

// Middleware
app.use(cors({
  origin: '*', // In production, you should restrict this to your frontend domain
  methods: ['GET', 'POST', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Simple mock data
const mockCandidates = [
  {
    _id: '1',
    firstName: 'John Doe',
    fullName: 'John Doe',
    age: 45,
    party: 'Democratic Party',
    bio: 'Experienced leader with a focus on education and healthcare.',
    image: 'default-candidate.jpg',
    symbol: 'default-symbol.jpg',
    votes: 10
  },
  {
    _id: '2',
    firstName: 'Jane Smith',
    fullName: 'Jane Smith',
    age: 50,
    party: 'Republican Party',
    bio: 'Business leader with a strong economic policy.',
    image: 'default-candidate.jpg',
    symbol: 'default-symbol.jpg',
    votes: 8
  },
  {
    _id: '3',
    firstName: 'Alex Johnson',
    fullName: 'Alex Johnson',
    age: 38,
    party: 'Independent',
    bio: 'Community organizer focused on social justice.',
    image: 'default-candidate.jpg',
    symbol: 'default-symbol.jpg',
    votes: 5
  }
];

// Mock data for invalid votes
const mockInvalidVotes = [];

// Mock voters data
const mockVoters = [
  {
    _id: '1',
    firstName: 'Test User',
    lastName: 'One',
    username: 'testuser',
    email: '<EMAIL>',
    age: 33,
    state: 'California',
    voteStatus: false
  },
  {
    _id: '2',
    firstName: 'Jane',
    lastName: 'Doe',
    username: 'janedoe',
    email: '<EMAIL>',
    age: 28,
    state: 'New York',
    voteStatus: true
  },
  {
    _id: '3',
    firstName: 'Bob',
    lastName: 'Smith',
    username: 'bobsmith',
    email: '<EMAIL>',
    age: 45,
    state: 'Texas',
    voteStatus: false
  },
  {
    _id: '4',
    firstName: 'Alice',
    lastName: 'Johnson',
    username: 'alicej',
    email: '<EMAIL>',
    age: 35,
    state: 'Florida',
    voteStatus: true
  },
  {
    _id: '5',
    firstName: 'Charlie',
    lastName: 'Brown',
    username: 'charlieb',
    email: '<EMAIL>',
    age: 50,
    state: 'California',
    voteStatus: false
  },
  {
    _id: '6',
    firstName: 'Diana',
    lastName: 'Prince',
    username: 'dianap',
    email: '<EMAIL>',
    age: 29,
    state: 'Washington',
    voteStatus: true
  },
  {
    _id: '7',
    firstName: 'Edward',
    lastName: 'Miller',
    username: 'edwardm',
    email: '<EMAIL>',
    age: 42,
    state: 'Oregon',
    voteStatus: false
  },
  {
    _id: '8',
    firstName: 'Fiona',
    lastName: 'Garcia',
    username: 'fionag',
    email: '<EMAIL>',
    age: 38,
    state: 'New York',
    voteStatus: true
  },
  {
    _id: '9',
    firstName: 'George',
    lastName: 'Wilson',
    username: 'georgew',
    email: '<EMAIL>',
    age: 55,
    state: 'Texas',
    voteStatus: false
  },
  {
    _id: '10',
    firstName: 'Hannah',
    lastName: 'Lee',
    username: 'hannahl',
    email: '<EMAIL>',
    age: 31,
    state: 'California',
    voteStatus: true
  }
];

// Mock elections data
const mockElections = [
  {
    _id: '1',
    name: 'Presidential Election 2024',
    description: 'General election for the President of the United States',
    startDate: '2024-11-03',
    endDate: '2024-11-03',
    status: 'upcoming'
  },
  {
    _id: '2',
    name: 'Senate Election 2024',
    description: 'Election for Senate seats',
    startDate: '2024-11-03',
    endDate: '2024-11-03',
    status: 'upcoming'
  },
  {
    _id: '3',
    name: 'Gubernatorial Election 2024',
    description: 'Election for state governors',
    startDate: '2024-11-03',
    endDate: '2024-11-03',
    status: 'upcoming'
  },
  {
    _id: '4',
    name: 'Local Council Election 2024',
    description: 'Election for local council members',
    startDate: '2024-10-15',
    endDate: '2024-10-15',
    status: 'upcoming'
  }
];

// Routes
app.get('/', (req, res) => {
  res.send('Simple API is running');
});

// Login route
app.post('/login', (req, res) => {
  const { username, password } = req.body;

  if (username === '<EMAIL>' && password === '123') {
    res.json({
      success: true,
      voterObject: {
        _id: '1',
        name: 'Test User',
        username: 'testuser',
        email: '<EMAIL>',
        age: 33,
        voteStatus: false
      }
    });
  } else {
    res.status(400).json({ success: false, message: 'Invalid credentials' });
  }
});

// Admin login route
app.post('/adminlogin', (req, res) => {
  const { username, password } = req.body;

  if (username === 'admin' && password === 'admin@123') {
    res.json({
      success: true,
      admin: {
        _id: '1',
        username: 'admin'
      }
    });
  } else {
    res.status(400).json({ success: false, message: 'Invalid credentials' });
  }
});

// Get all candidates
app.get('/getCandidate', (req, res) => {
  res.json({
    success: true,
    candidate: mockCandidates
  });
});

// Temporarily disable face recognition module due to dependency issues
// const faceRecognition = require('./biometrics/faceRecognition');

// Initialize face recognition models
// (async () => {
//   try {
//     await faceRecognition.loadModels();
//     console.log('Face recognition models loaded successfully');
//   } catch (error) {
//     console.error('Error loading face recognition models:', error);
//   }
// })();

// Mock face recognition module for testing
const faceRecognition = {
  compareMultipleFaces: async (selfieBuffer, idBuffer, videoBuffer, options) => {
    console.log('Mock face comparison running...');

    // Generate a random similarity percentage between 60% and 80%
    const selfieToIdSimilarity = Math.random() * 0.2 + 0.6; // 0.6 to 0.8
    const selfieToVideoSimilarity = Math.random() * 0.2 + 0.6; // 0.6 to 0.8
    const idToVideoSimilarity = Math.random() * 0.2 + 0.6; // 0.6 to 0.8

    const averageSimilarity = (selfieToIdSimilarity + selfieToVideoSimilarity + idToVideoSimilarity) / 3;
    const weightedSimilarity = (
      selfieToIdSimilarity * 0.3 +
      selfieToVideoSimilarity * 0.5 +
      idToVideoSimilarity * 0.2
    );

    return {
      success: true,
      isMatch: true,
      averageSimilarity: averageSimilarity,
      averageSimilarityPercentage: Math.round(averageSimilarity * 100),
      weightedSimilarityPercentage: Math.round(weightedSimilarity * 100),
      comparisons: {
        selfieToId: {
          success: true,
          isMatch: true,
          similarity: selfieToIdSimilarity,
          similarityPercentage: Math.round(selfieToIdSimilarity * 100)
        },
        selfieToVideo: {
          success: true,
          isMatch: true,
          similarity: selfieToVideoSimilarity,
          similarityPercentage: Math.round(selfieToVideoSimilarity * 100)
        },
        idToVideo: {
          success: true,
          isMatch: true,
          similarity: idToVideoSimilarity,
          similarityPercentage: Math.round(idToVideoSimilarity * 100)
        }
      }
    };
  }
};

// Advanced voter verification endpoint with face matching
app.post('/verify-voter', upload.fields([
  { name: 'selfie', maxCount: 1 },
  { name: 'idImage', maxCount: 1 },
  { name: 'videoFrame', maxCount: 1 } // Required for enhanced security
]), async (req, res) => {
  try {
    console.log('Received verification request');

    // Check if required files are provided
    if (!req.files || !req.files.selfie || !req.files.idImage) {
      return res.status(400).json({
        success: false,
        message: 'Both selfie and ID image are required for verification',
        verificationStep: 'file_validation'
      });
    }

    // Make video frame mandatory for enhanced security
    if (!req.files.videoFrame || req.files.videoFrame.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Live video capture is required for verification',
        verificationStep: 'file_validation'
      });
    }

    console.log('All required files provided. Processing...');

    // Get file paths
    const selfiePath = req.files.selfie[0].path;
    const idImagePath = req.files.idImage[0].path;
    const videoFramePath = req.files.videoFrame[0].path;

    // Read file buffers
    const selfieBuffer = fs.readFileSync(selfiePath);
    const idImageBuffer = fs.readFileSync(idImagePath);
    const videoFrameBuffer = fs.readFileSync(videoFramePath);

    // Set face matching thresholds (60% to 80% similarity)
    const options = {
      minThreshold: 0.6, // 60% similarity
      maxThreshold: 0.8  // 80% similarity
    };

    console.log('Starting face comparison process...');

    // Compare all three: selfie, ID, and video frame
    const faceComparisonResult = await faceRecognition.compareMultipleFaces(
      selfieBuffer,
      idImageBuffer,
      videoFrameBuffer,
      options
    );

    console.log('Face comparison complete:', JSON.stringify({
      success: faceComparisonResult.success,
      isMatch: faceComparisonResult.isMatch,
      averageSimilarityPercentage: faceComparisonResult.averageSimilarityPercentage,
      failureReason: faceComparisonResult.failureReason
    }));

    // Check if face detection was successful
    if (!faceComparisonResult.success) {
      return res.status(403).json({
        success: false,
        message: faceComparisonResult.error || 'Face detection failed. Please ensure your face is clearly visible in all images.',
        verificationStep: faceComparisonResult.verificationStep || 'face_detection',
        image: faceComparisonResult.image || 'unknown',
        details: faceComparisonResult
      });
    }

    // Check if faces match within the threshold
    if (!faceComparisonResult.isMatch) {
      // Record the invalid vote attempt for security purposes
      const invalidVoteRecord = {
        _id: Date.now().toString(),
        violationType: 'face_mismatch',
        violationDetails: faceComparisonResult.failureReason || 'Face matching failed',
        timestamp: new Date().toISOString(),
        similarityPercentage: faceComparisonResult.averageSimilarityPercentage
      };

      mockInvalidVotes.push(invalidVoteRecord);

      return res.status(403).json({
        success: false,
        message: faceComparisonResult.failureReason ||
          `Face matching failed. Similarity (${faceComparisonResult.averageSimilarityPercentage}%) is outside the required range (60%-80%).`,
        verificationStep: 'face_matching',
        similarityPercentage: faceComparisonResult.averageSimilarityPercentage,
        weightedSimilarityPercentage: faceComparisonResult.weightedSimilarityPercentage,
        thresholds: {
          min: Math.round(options.minThreshold * 100),
          max: Math.round(options.maxThreshold * 100)
        },
        details: {
          comparisons: faceComparisonResult.comparisons,
          invalidVoteId: invalidVoteRecord._id
        }
      });
    }

    // Generate a secure verification ID
    const verificationId = `verify-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

    // Return success with similarity details
    res.json({
      success: true,
      message: `Voter verified successfully with ${faceComparisonResult.averageSimilarityPercentage}% similarity`,
      similarityPercentage: faceComparisonResult.averageSimilarityPercentage,
      weightedSimilarityPercentage: faceComparisonResult.weightedSimilarityPercentage,
      verificationId: verificationId,
      details: {
        thresholds: {
          min: Math.round(options.minThreshold * 100),
          max: Math.round(options.maxThreshold * 100)
        },
        comparisons: {
          selfieToId: faceComparisonResult.comparisons.selfieToId ? {
            similarityPercentage: faceComparisonResult.comparisons.selfieToId.similarityPercentage
          } : null,
          selfieToVideo: faceComparisonResult.comparisons.selfieToVideo ? {
            similarityPercentage: faceComparisonResult.comparisons.selfieToVideo.similarityPercentage
          } : null,
          idToVideo: faceComparisonResult.comparisons.idToVideo ? {
            similarityPercentage: faceComparisonResult.comparisons.idToVideo.similarityPercentage
          } : null
        }
      }
    });
  } catch (error) {
    console.error('Error verifying voter:', error);
    res.status(500).json({
      success: false,
      message: 'Error during verification',
      error: error.message
    });
  }
});

// Update candidate votes with verification
app.patch('/getCandidate/:id', (req, res) => {
  const candidate = mockCandidates.find(c => c._id === req.params.id);

  if (!candidate) {
    return res.status(404).json({ success: false, message: 'Candidate not found' });
  }

  // Check if verification was done (in a real app, we would validate the verification token)
  const { verificationId } = req.body;

  if (!verificationId) {
    return res.status(403).json({
      success: false,
      message: 'Voter verification required before voting'
    });
  }

  candidate.votes += 1;

  res.json({
    success: true,
    votes: candidate.votes
  });
});

// Update voter status
app.patch('/updateVoter/:id', (req, res) => {
  res.json({
    success: true,
    voter: {
      _id: '1',
      voteStatus: true
    }
  });
});

// Get dashboard data
app.get('/getDashboardData', (req, res) => {
  res.json({
    success: true,
    DashboardData: {
      voterCount: 10,
      candidateCount: 3,
      votersVoted: 5
    }
  });
});

// Get all voters
app.get('/getVoter', (req, res) => {
  res.json({
    success: true,
    voter: mockVoters
  });
});

// Get voter by ID
app.get('/getVoterbyID/:id', (req, res) => {
  const voter = mockVoters.find(v => v._id === req.params.id);

  if (!voter) {
    return res.status(404).json({ success: false, message: 'Voter not found' });
  }

  res.json({
    success: true,
    voter
  });
});

// Create a new candidate
app.post('/createCandidate', upload.fields([
  { name: 'image', maxCount: 1 },
  { name: 'symbol', maxCount: 1 }
]), (req, res) => {
  try {
    const { firstName, lastName, age, party, bio } = req.body;

    // Generate a new ID (in a real app, this would be handled by the database)
    const newId = (mockCandidates.length + 1).toString();

    // Handle file uploads
    let imagePath = 'default-candidate.jpg';
    let symbolPath = 'default-symbol.jpg';

    if (req.files) {
      if (req.files.image && req.files.image.length > 0) {
        imagePath = req.files.image[0].filename;
      }

      if (req.files.symbol && req.files.symbol.length > 0) {
        symbolPath = req.files.symbol[0].filename;
      }
    }

    const newCandidate = {
      _id: newId,
      firstName,
      fullName: `${firstName} ${lastName || ''}`,
      age: parseInt(age),
      party,
      bio,
      image: imagePath,
      symbol: symbolPath,
      votes: 0
    };

    mockCandidates.push(newCandidate);

    res.status(201).json({
      success: true,
      candidate: newCandidate
    });
  } catch (error) {
    console.error('Error creating candidate:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating candidate',
      error: error.message
    });
  }
});

// Get all elections
app.get('/getElections', (req, res) => {
  res.json({
    success: true,
    elections: mockElections
  });
});

// Create a new election
app.post('/createElection', (req, res) => {
  const { name, description, startDate, endDate } = req.body;

  // Generate a new ID (in a real app, this would be handled by the database)
  const newId = (mockElections.length + 1).toString();

  const newElection = {
    _id: newId,
    name,
    description,
    startDate,
    endDate,
    status: 'upcoming'
  };

  mockElections.push(newElection);

  res.status(201).json({
    success: true,
    election: newElection
  });
});

// Record an invalid vote due to security violation
app.post('/recordInvalidVote', (req, res) => {
  try {
    const { voterId, candidateId, violationType, violationDetails } = req.body;

    // Validate required fields
    if (!voterId || !candidateId || !violationType || !violationDetails) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields for invalid vote record'
      });
    }

    // Create a new invalid vote record
    const newInvalidVote = {
      _id: Date.now().toString(),
      voterId,
      candidateId,
      violationType,
      violationDetails,
      timestamp: new Date().toISOString(),
      evidenceData: req.body.evidenceData || null
    };

    // Add to mock data
    mockInvalidVotes.push(newInvalidVote);

    // Return success
    res.status(201).json({
      success: true,
      message: 'Invalid vote recorded successfully',
      invalidVote: newInvalidVote
    });
  } catch (error) {
    console.error('Error recording invalid vote:', error);
    res.status(500).json({
      success: false,
      message: 'Error recording invalid vote',
      error: error.message
    });
  }
});

// Get all invalid votes (for admin dashboard)
app.get('/getInvalidVotes', (req, res) => {
  res.json({
    success: true,
    invalidVotes: mockInvalidVotes
  });
});

// Add a route for health check
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Server is healthy' });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Server time: ${new Date().toISOString()}`);
});
